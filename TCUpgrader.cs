using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Oxide.Core;
using Oxide.Core.Plugins;
using Oxide.Game.Rust.Cui;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("TC Upgrader", "Frizzo420", "1.0.0")]
    [Description("Adds upgrade buttons to Tool Cupboards")]
    class TCUpgrader : RustPlugin
    {
        #region Fields

        [PluginReference] private Plugin ImageLibrary;

        private const string PERMISSION_USE = "tcupgrader.use";
        
        private Dictionary<ulong, string> activeUIs = new Dictionary<ulong, string>();
        private Dictionary<string, string> imageCache = new Dictionary<string, string>();

        private const string WOOD_IMAGE = "wood";
        private const string STONE_IMAGE = "stones";
        private const string METAL_IMAGE = "metal.fragments";
        private const string ARMORED_IMAGE = "metal.refined";

        #endregion

        #region Configuration

        private Configuration config;

        private class Configuration
        {
            // Version structure at the top of the config
            [JsonProperty("Config Version (Don't Edit This)")]
            public VersionInfo ConfigVersion { get; set; } = new VersionInfo { Major = 1, Minor = 0, Patch = 0 };
            
            [JsonProperty("Upgrade Range")]
            public float UpgradeRange = 100f;

            // Resource Settings
            [JsonProperty("Require Resources For Upgrades")]
            public bool RequireResourcesForUpgrades = true;
            
            [JsonProperty("Require Resources For Repairs")]
            public bool RequireResourcesForRepairs = true;
            
            [JsonProperty("Enable Cost Multiplier")]
            public bool EnableCostMultiplier = false;
            
            [JsonProperty("Cost Multiplier")]
            public float CostMultiplier = 1.0f;
            
            [JsonProperty("Repair Cost Multiplier")]
            public float RepairCostMultiplier = 0.5f;
            
            [JsonProperty("Enable Permission Requirement")]
            public bool EnablePermission = true;
            
            // UI Positioning
            [JsonProperty("UI Position")]
            public string UIPosition = "top";
            
            [JsonProperty("UI Height")]
            public float UIHeight = 0.05f;
            
            [JsonProperty("UI Width")]
            public float UIWidth = 0.1f;
            
            [JsonProperty("UI Horizontal Offset")]
            public float UIHorizontalOffset = 0.31f;
            
            [JsonProperty("UI Vertical Offset")]
            public float UIVerticalOffset = -0.25f;
            
            // Button Settings
            [JsonProperty("Button Size")]
            public float ButtonSize = 0.27f;
            
            [JsonProperty("Button Spacing")]
            public float ButtonSpacing = 0.02f;
            
            [JsonProperty("Button Vertical Position")]
            public float ButtonVerticalPosition = 0.8f;
            
            [JsonProperty("Button Row Height")]
            public float ButtonRowHeight = 0.6f;
            
            // Title Settings
            [JsonProperty("Show Title")]
            public bool ShowTitle = true;
            
            [JsonProperty("Title Text")]
            public string TitleText = "Upgrade Building";
            
            [JsonProperty("Title Font Size")]
            public int TitleFontSize = 12;
            
            [JsonProperty("Title Height")]
            public float TitleHeight = 0.4f;
            
            // Button Colors
            [JsonProperty("Wood Button Color")]
            public string WoodButtonColor = "0.7 0.5 0.2 1";
            
            [JsonProperty("Stone Button Color")]
            public string StoneButtonColor = "0.5 0.5 0.5 1";
            
            [JsonProperty("Metal Button Color")]
            public string MetalButtonColor = "0.7 0.7 0.7 1";
            
            [JsonProperty("Armored Button Color")]
            public string ArmoredButtonColor = "0.3 0.3 0.3 1";
            
            // Background Settings
            [JsonProperty("Background Color")]
            public string BackgroundColor = "0.1 0.1 0.1 0.8";
            
            // Command Settings
            [JsonProperty("Enable Chat Command")]
            public bool EnableChatCommand = true;
            
            [JsonProperty("Chat Command")]
            public string ChatCommand = "tcupgrade";
            
            [JsonProperty("Position Command")]
            public string PositionCommand = "tcupgrader.position";
            
            [JsonProperty("Reload Command")]
            public string ReloadCommand = "tcupgrader.reload";
            
            [JsonProperty("Admin Permission")]
            public string AdminPermission = "tcupgrader.admin";
            
            // Repair Settings
            [JsonProperty("Enable Repair Option")]
            public bool EnableRepair = true;
            
            [JsonProperty("Repair Button Color")]
            public string RepairButtonColor = "0.2 0.6 0.2 1";
            
            [JsonProperty("Repair Command")]
            public string RepairCommand = "tcrepair";
            
            [JsonProperty("Show Repair Button")]
            public bool ShowRepairButton = true;
            
            // Sound Settings
            [JsonProperty("Enable Repair Sounds")]
            public bool EnableRepairSounds = true;
            
            [JsonProperty("Enable Upgrade Sounds")]
            public bool EnableUpgradeSounds = true;
            
            [JsonProperty("Sound Volume")]
            public float SoundVolume = 1.0f;
            
            // Custom Image Settings
            [JsonProperty("Use Custom Images")]
            public bool UseCustomImages = false;
            
            [JsonProperty("Custom Wood Image URL")]
            public string CustomWoodImageUrl = "https://rustlabs.com/img/items180/wood.png";
            
            [JsonProperty("Custom Stone Image URL")]
            public string CustomStoneImageUrl = "https://rustlabs.com/img/items180/stones.png";
            
            [JsonProperty("Custom Metal Image URL")]
            public string CustomMetalImageUrl = "https://rustlabs.com/img/items180/metal.fragments.png";
            
            [JsonProperty("Custom Armored Image URL")]
            public string CustomArmoredImageUrl = "https://rustlabs.com/img/items180/metal.refined.png";
            
            [JsonProperty("Custom Repair Image URL")]
            public string CustomRepairImageUrl = "https://rustlabs.com/img/items180/hammer.png";
            
            // Message Settings
            [JsonProperty("Enable Custom Messages")]
            public bool EnableCustomMessages = false;
            
            [JsonProperty("Success Message Prefix")]
            public string SuccessMessagePrefix = "<color=#00FF00>✓</color> ";
            
            [JsonProperty("Error Message Prefix")]
            public string ErrorMessagePrefix = "<color=#FF0000>✗</color> ";
            
            [JsonProperty("No Permission Message")]
            public string NoPermissionMessage = "You don't have permission to use this feature.";
            
            [JsonProperty("No TC Message")]
            public string NoTCMessage = "You need to be authorized on a Tool Cupboard to use this feature.";
            
            [JsonProperty("No Blocks Message")]
            public string NoBlocksMessage = "No blocks found that can be upgraded to this grade.";
            
            [JsonProperty("No Repair Blocks Message")]
            public string NoRepairBlocksMessage = "No blocks found that need repair.";
            
            [JsonProperty("Insufficient Resources Message")]
            public string InsufficientResourcesMessage = "You don't have enough resources to upgrade any blocks.";
            
            [JsonProperty("Upgrade Success Message")]
            public string UpgradeSuccessMessage = "Successfully upgraded {0} building blocks to {1}.";
            
            [JsonProperty("Repair Success Message")]
            public string RepairSuccessMessage = "Successfully repaired {0} building blocks!";

            // Message Image Settings
            [JsonProperty("Use Message Images")]
            public bool UseMessageImages = false;
            
            [JsonProperty("Success Message Image URL")]
            public string SuccessMessageImageUrl = "https://i.imgur.com/JZlVGaz.png";
            
            [JsonProperty("Error Message Image URL")]
            public string ErrorMessageImageUrl = "https://i.imgur.com/SgXSyQi.png";
            
            [JsonProperty("Message Image Size")]
            public int MessageImageSize = 20;

            [JsonProperty("Allow Downgrading")]
            public bool AllowDowngrading = false;

            // Button Text Settings
            [JsonProperty("Button Text Size")]
            public int ButtonTextSize = 8;

            // Nested class for version information
            public class VersionInfo
            {
                [JsonProperty("Major")]
                public int Major { get; set; }
                
                [JsonProperty("Minor")]
                public int Minor { get; set; }
                
                [JsonProperty("Patch")]
                public int Patch { get; set; }
                
                public override string ToString()
                {
                    return $"{Major}.{Minor}.{Patch}";
                }
                
                public static VersionInfo Parse(string version)
                {
                    var parts = version.Split('.');
                    return new VersionInfo
                    {
                        Major = parts.Length > 0 ? int.Parse(parts[0]) : 1,
                        Minor = parts.Length > 1 ? int.Parse(parts[1]) : 0,
                        Patch = parts.Length > 2 ? int.Parse(parts[2]) : 0
                    };
                }
            }
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null) throw new Exception();
                
                // Check if config version matches plugin version and update if needed
                UpdateConfigValues();
                
                SaveConfig();
            }
            catch
            {
                PrintWarning("Configuration file is corrupt, creating new configuration file");
                LoadDefaultConfig();
            }
        }

        private void UpdateConfigValues()
        {
            // Get current plugin version from Info attribute
            var infoAttribute = GetType().GetCustomAttributes(typeof(InfoAttribute), true)
                .Cast<InfoAttribute>()
                .FirstOrDefault();
            
            string pluginVersionStr = "1.0.0";
            if (infoAttribute != null)
            {
                pluginVersionStr = infoAttribute.Version.ToString();
            }
            
            // Parse plugin version into VersionInfo
            var pluginVersion = Configuration.VersionInfo.Parse(pluginVersionStr);
            
            // Check if config version matches plugin version
            if (config.ConfigVersion.Major == pluginVersion.Major && 
                config.ConfigVersion.Minor == pluginVersion.Minor && 
                config.ConfigVersion.Patch == pluginVersion.Patch)
                return;
            
            PrintWarning($"Configuration version mismatch (Config: {config.ConfigVersion}, Plugin: {pluginVersionStr}). Updating configuration...");
            
            // Add version-specific updates here
            if (CompareVersionInfos(config.ConfigVersion, new Configuration.VersionInfo { Major = 1, Minor = 0, Patch = 0 }) < 0)
            {
                // Settings added in version 1.0.0
                // This is just a placeholder since we're starting at 1.0.0
            }
            
            // For version 1.0.0 - ensure ButtonTextSize exists
            if (!config.GetType().GetProperties().Any(p => p.Name == "ButtonTextSize"))
            {
                PrintWarning("Adding 'Button Text Size' configuration option");
                config.ButtonTextSize = 9;
            }
            
            // Update config version to match plugin version
            config.ConfigVersion = pluginVersion;
            PrintWarning("Configuration update completed!");
        }

        // Helper method to compare VersionInfo objects
        private int CompareVersionInfos(Configuration.VersionInfo v1, Configuration.VersionInfo v2)
        {
            if (v1.Major != v2.Major)
                return v1.Major.CompareTo(v2.Major);
            
            if (v1.Minor != v2.Minor)
                return v1.Minor.CompareTo(v2.Minor);
            
            return v1.Patch.CompareTo(v2.Patch);
        }

        // Helper method to check if a property exists in the config
        private bool HasConfigProperty(string propertyName)
        {
            return typeof(Configuration).GetProperties()
                .Any(prop => prop.Name == propertyName || 
                            prop.GetCustomAttributes(typeof(JsonPropertyAttribute), true)
                                .Cast<JsonPropertyAttribute>()
                                .Any(attr => attr.PropertyName == propertyName));
        }

        protected override void LoadDefaultConfig()
        {
            // Get current plugin version
            var infoAttribute = GetType().GetCustomAttributes(typeof(InfoAttribute), true)
                .Cast<InfoAttribute>()
                .FirstOrDefault();
            
            string pluginVersionStr = "1.0.0";
            if (infoAttribute != null)
            {
                pluginVersionStr = infoAttribute.Version.ToString();
            }
            
            // Parse plugin version into VersionInfo
            var pluginVersion = Configuration.VersionInfo.Parse(pluginVersionStr);

            config = new Configuration
            {
                // Set config version to match plugin version
                ConfigVersion = pluginVersion,
                
                // Resource Settings
                UpgradeRange = 100f,
                RequireResourcesForUpgrades = true,
                RequireResourcesForRepairs = true,
                EnableCostMultiplier = false,
                CostMultiplier = 1.0f,
                RepairCostMultiplier = 0.5f,
                AllowDowngrading = false,
                
                // Permission Settings
                EnablePermission = true,
                AdminPermission = "tcupgrader.admin",
                
                // UI Positioning
                UIPosition = "top",
                UIHeight = 0.05f,
                UIWidth = 0.1f,
                UIHorizontalOffset = 0.25f,
                UIVerticalOffset = -0.25f,
                ButtonSize = 0.27f,
                ButtonSpacing = 0.02f,
                ButtonVerticalPosition = 0.8f,
                ButtonRowHeight = 0.6f,
                ButtonTextSize = 9,
                
                // Title Settings
                ShowTitle = true,
                TitleText = "Upgrade Building",
                TitleFontSize = 12,
                TitleHeight = 0.4f,
                
                // Button Colors
                WoodButtonColor = "0.2 0.6 0.2 1",
                StoneButtonColor = "0.2 0.6 0.2 1",
                MetalButtonColor = "0.2 0.6 0.2 1",
                ArmoredButtonColor = "0.2 0.6 0.2 1",
                BackgroundColor = "0.1 0.1 0.1 0.8",
                
                // Command Settings
                EnableChatCommand = true,
                ChatCommand = "tcupgrade",
                PositionCommand = "tcupgrader.position",
                ReloadCommand = "tcupgrader.reload",
                
                // Repair Settings
                EnableRepair = true,
                RepairButtonColor = "0.2 0.6 0.2 1",
                RepairCommand = "tcrepair",
                ShowRepairButton = true,
                
                // Sound Settings
                EnableRepairSounds = true,
                EnableUpgradeSounds = true,
                SoundVolume = 1.0f,
                
                // Custom Image Settings
                UseCustomImages = false,
                CustomWoodImageUrl = "https://rustlabs.com/img/items180/wood.png",
                CustomStoneImageUrl = "https://rustlabs.com/img/items180/stones.png",
                CustomMetalImageUrl = "https://rustlabs.com/img/items180/metal.fragments.png",
                CustomArmoredImageUrl = "https://rustlabs.com/img/items180/metal.refined.png",
                CustomRepairImageUrl = "https://rustlabs.com/img/items180/hammer.png",
                
                // Message Settings
                EnableCustomMessages = false,
                SuccessMessagePrefix = "<color=#00FF00>✓</color> ",
                ErrorMessagePrefix = "<color=#FF0000>✗</color> ",
                NoPermissionMessage = "You don't have permission to use this feature.",
                NoTCMessage = "You need to be authorized on a Tool Cupboard to use this feature.",
                NoBlocksMessage = "No blocks found that can be upgraded to this grade.",
                NoRepairBlocksMessage = "No blocks found that need repair.",
                InsufficientResourcesMessage = "You don't have enough resources to upgrade any blocks.",
                UpgradeSuccessMessage = "Successfully upgraded {0} building blocks to {1}.",
                RepairSuccessMessage = "Successfully repaired {0} building blocks!",
                
                // Message Image Settings
                UseMessageImages = false,
                SuccessMessageImageUrl = "https://i.imgur.com/JZlVGaz.png",
                ErrorMessageImageUrl = "https://i.imgur.com/SgXSyQi.png",
                MessageImageSize = 20
            };
            
            SaveConfig();
        }

        protected override void SaveConfig()
        {
            PrintWarning($"Saving config with version: {config.ConfigVersion}");
            
            // Use Oxide's Config.WriteObject with pretty printing
            Config.WriteObject(config, true);
        }

        #endregion

        #region Oxide Hooks

        private void Init()
        {
            permission.RegisterPermission(PERMISSION_USE, this);
            permission.RegisterPermission(config.AdminPermission, this);
            
            if (config.EnableChatCommand)
            {
                cmd.AddChatCommand(config.ChatCommand, this, "CommandUpgrade");
                
                if (config.EnableRepair)
                    cmd.AddChatCommand(config.RepairCommand, this, "CommandRepair");
            }
            
            cmd.AddChatCommand(config.PositionCommand, this, "CommandPositionUI");
            cmd.AddChatCommand(config.ReloadCommand, this, "CommandReload");
            
            // Register console commands
            cmd.AddConsoleCommand("tcupgrade", this, "CommandUpgrade");
            cmd.AddConsoleCommand("tcrepair", this, "ConsoleRepair");
        }

        private void OnServerInitialized()
        {
            if (ImageLibrary == null)
            {
                PrintError("ImageLibrary is not loaded! Plugin will not function correctly.");
                return;
            }

            // Register images with ImageLibrary
            RegisterImages();
        }

        private void Unload()
        {
            foreach (var player in BasePlayer.activePlayerList)
            {
                DestroyUI(player);
            }
        }

        private void OnLootEntity(BasePlayer player, BuildingPrivlidge privilege)
        {
            if (privilege == null || player == null) return;
            
            if (config.EnablePermission && !permission.UserHasPermission(player.UserIDString, PERMISSION_USE))
                return;

            // Remove debug message
            // PrintWarning($"OnLootEntity fired for player {player.displayName} on TC");
            
            // Use a slightly longer delay to ensure the TC UI is fully loaded
            timer.Once(0.5f, () => CreateUI(player));
        }

        private void OnPlayerLootEnd(PlayerLoot inventory)
        {
            if (inventory?.entitySource is BuildingPrivlidge && inventory.baseEntity != null)
            {
                DestroyUI(inventory.baseEntity as BasePlayer);
            }
        }

        #endregion

        #region Core Methods

        private void RegisterImages()
        {
            // Register default images with ImageLibrary
            if (!config.UseCustomImages)
            {
                // Use default RustLabs images
                ImageLibrary.Call("AddImage", "https://rustlabs.com/img/items180/wood.png", WOOD_IMAGE);
                ImageLibrary.Call("AddImage", "https://rustlabs.com/img/items180/stones.png", STONE_IMAGE);
                ImageLibrary.Call("AddImage", "https://rustlabs.com/img/items180/metal.fragments.png", METAL_IMAGE);
                ImageLibrary.Call("AddImage", "https://rustlabs.com/img/items180/metal.refined.png", ARMORED_IMAGE);
                ImageLibrary.Call("AddImage", "https://rustlabs.com/img/items180/hammer.png", "hammer");
            }
            else
            {
                // Use custom images from config
                ImageLibrary.Call("AddImage", config.CustomWoodImageUrl, WOOD_IMAGE);
                ImageLibrary.Call("AddImage", config.CustomStoneImageUrl, STONE_IMAGE);
                ImageLibrary.Call("AddImage", config.CustomMetalImageUrl, METAL_IMAGE);
                ImageLibrary.Call("AddImage", config.CustomArmoredImageUrl, ARMORED_IMAGE);
                ImageLibrary.Call("AddImage", config.CustomRepairImageUrl, "hammer");
            }
        }

        private string GetImage(string imageName)
        {
            if (imageCache.ContainsKey(imageName))
                return imageCache[imageName];

            string imageId = (string)ImageLibrary.Call("GetImage", imageName);
            imageCache[imageName] = imageId;
            return imageId;
        }

        private void CreateUI(BasePlayer player)
        {
            DestroyUI(player);

            CuiElementContainer container = new CuiElementContainer();
            
            // Calculate UI position based on configuration
            float xMin = 0f, xMax = 1f, yMin = 0f, yMax = 1f;
            
            switch (config.UIPosition.ToLower())
            {
                case "top":
                    yMin = 1f - config.UIHeight;
                    yMax = 1f;
                    xMin = (1f - config.UIWidth) / 2f + config.UIHorizontalOffset;
                    xMax = xMin + config.UIWidth;
                    break;
                case "left":
                    xMin = 0f;
                    xMax = config.UIWidth;
                    yMin = (1f - config.UIHeight) / 2f + config.UIVerticalOffset;
                    yMax = yMin + config.UIHeight;
                    break;
                case "right":
                    xMax = 1f;
                    xMin = 1f - config.UIWidth;
                    yMin = (1f - config.UIHeight) / 2f + config.UIVerticalOffset;
                    yMax = yMin + config.UIHeight;
                    break;
                case "bottom":
                default:
                    yMin = 0f;
                    yMax = config.UIHeight;
                    xMin = (1f - config.UIWidth) / 2f + config.UIHorizontalOffset;
                    xMax = xMin + config.UIWidth;
                    break;
            }
            
            // Apply vertical offset
            yMin += config.UIVerticalOffset;
            yMax += config.UIVerticalOffset;
            
            // Create main panel
            container.Add(new CuiPanel
            {
                RectTransform = { AnchorMin = $"{xMin} {yMin}", AnchorMax = $"{xMax} {yMax}" },
                Image = { Color = config.BackgroundColor }
            }, "Overlay", "TCUpgrader_Main");

            // Add title if enabled
            if (config.ShowTitle)
            {
                container.Add(new CuiLabel
                {
                    RectTransform = { AnchorMin = $"0 {1 - config.TitleHeight}", AnchorMax = "1 1" },
                    Text = { Text = config.TitleText, FontSize = config.TitleFontSize, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
                }, "TCUpgrader_Main");
            }

            // Calculate button positions
            float buttonWidth = config.ButtonSize;
            float spacing = config.ButtonSpacing;
            
            // Determine number of buttons (4 upgrade buttons + optional repair button)
            int buttonCount = config.ShowRepairButton && config.EnableRepair ? 5 : 4;
            float totalWidth = (buttonWidth * buttonCount) + (spacing * (buttonCount - 1));
            float startX = (1 - totalWidth) / 2;
            
            // Calculate button vertical position
            float buttonYMin = config.ShowTitle ? (1 - config.TitleHeight - config.ButtonRowHeight) * config.ButtonVerticalPosition : (1 - config.ButtonRowHeight) * config.ButtonVerticalPosition;
            float buttonYMax = buttonYMin + config.ButtonRowHeight;

            // Wood Button
            CreateUpgradeButton(container, "Wood", WOOD_IMAGE, BuildingGrade.Enum.Wood, 
                startX, startX + buttonWidth, buttonYMin, buttonYMax, config.WoodButtonColor);

            // Stone Button
            CreateUpgradeButton(container, "Stone", STONE_IMAGE, BuildingGrade.Enum.Stone, 
                startX + buttonWidth + spacing, startX + buttonWidth * 2 + spacing, buttonYMin, buttonYMax, config.StoneButtonColor);

            // Metal Button
            CreateUpgradeButton(container, "Metal", METAL_IMAGE, BuildingGrade.Enum.Metal, 
                startX + (buttonWidth + spacing) * 2, startX + buttonWidth * 3 + spacing * 2, buttonYMin, buttonYMax, config.MetalButtonColor);

            // Armored Button
            CreateUpgradeButton(container, "Armored", ARMORED_IMAGE, BuildingGrade.Enum.TopTier, 
                startX + (buttonWidth + spacing) * 3, startX + buttonWidth * 4 + spacing * 3, buttonYMin, buttonYMax, config.ArmoredButtonColor);

            // Add Repair Button if enabled
            if (config.ShowRepairButton && config.EnableRepair)
            {
                CreateRepairButton(container, "Repair", 
                    startX + (buttonWidth + spacing) * 4, startX + buttonWidth * 5 + spacing * 4, buttonYMin, buttonYMax, config.RepairButtonColor);
            }

            string uiId = CuiHelper.GetGuid();
            activeUIs[player.userID] = uiId;
            
            CuiHelper.AddUi(player, container);
        }

        private void CreateUpgradeButton(CuiElementContainer container, string name, string image, BuildingGrade.Enum grade, 
            float xMin, float xMax, float yMin, float yMax, string color)
        {
            string buttonName = $"TCUpgrader_Button_{grade}";
            
            // Button panel
            container.Add(new CuiPanel
            {
                RectTransform = { AnchorMin = $"{xMin} {yMin}", AnchorMax = $"{xMax} {yMax}" },
                Image = { Color = color }
            }, "TCUpgrader_Main", buttonName);

            // Image
            container.Add(new CuiElement
            {
                Parent = buttonName,
                Components =
                {
                    new CuiRawImageComponent { Png = GetImage(image), Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.1 0.3", AnchorMax = "0.9 0.9" }
                }
            });

            // Label - updated to use the new ButtonTextSize config
            container.Add(new CuiLabel
            {
                RectTransform = { AnchorMin = "0 0", AnchorMax = "1 0.3" },
                Text = { Text = name, FontSize = config.ButtonTextSize, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
            }, buttonName);

            // Button
            container.Add(new CuiButton
            {
                RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1" },
                Button = { Color = "0 0 0 0", Command = $"tcupgrade {grade}" },
                Text = { Text = "" }
            }, buttonName);
        }

        private void CreateRepairButton(CuiElementContainer container, string name, 
            float xMin, float xMax, float yMin, float yMax, string color)
        {
            string buttonName = $"TCUpgrader_Repair";
            
            // Add button panel
            container.Add(new CuiPanel
            {
                RectTransform = { AnchorMin = $"{xMin} {yMin}", AnchorMax = $"{xMax} {yMax}" },
                Image = { Color = color }
            }, "TCUpgrader_Main", buttonName);
            
            // Add repair icon if using ImageLibrary
            container.Add(new CuiElement
            {
                Parent = buttonName,
                Components =
                {
                    new CuiRawImageComponent { Png = GetImage("hammer"), Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.1 0.3", AnchorMax = "0.9 0.9" }
                }
            });
            
            // Add button text (single label)
            container.Add(new CuiLabel
            {
                RectTransform = { AnchorMin = "0 0", AnchorMax = "1 0.3" },
                Text = { Text = name, FontSize = config.ButtonTextSize, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
            }, buttonName);
            
            // Add button action
            container.Add(new CuiButton
            {
                RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1" },
                Button = { Command = config.RepairCommand, Color = "0 0 0 0" },
                Text = { Text = "" }
            }, buttonName);
        }

        private void DestroyUI(BasePlayer player)
        {
            if (player == null) return;
            
            // Remove debug message
            // PrintWarning($"Destroying UI for player {player.displayName}");
            
            CuiHelper.DestroyUi(player, "TCUpgrader_Main");
            activeUIs.Remove(player.userID);
        }

        [ConsoleCommand("tcupgrade")]
        private void CommandUpgrade(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null) return;

            if (config.EnablePermission && !permission.UserHasPermission(player.UserIDString, PERMISSION_USE))
                return;

            if (arg.Args == null || arg.Args.Length == 0) return;

            if (!Enum.TryParse(arg.Args[0], out BuildingGrade.Enum grade))
                return;

            UpgradeBuilding(player, grade);
        }

        private void UpgradeBuilding(BasePlayer player, BuildingGrade.Enum grade)
        {
            BuildingPrivlidge tc = player.GetBuildingPrivilege();
            if (tc == null)
            {
                SendReply(player, config.NoTCMessage);
                return;
            }

            List<BuildingBlock> blocks = new List<BuildingBlock>();
            Vis.Entities(tc.transform.position, config.UpgradeRange, blocks, LayerMask.GetMask("Construction"));

            // Filter blocks that can be upgraded
            List<BuildingBlock> upgradeableBlocks = new List<BuildingBlock>();
            foreach (var block in blocks)
            {
                if (block.buildingID == tc.buildingID && block.grade != grade)
                {
                    upgradeableBlocks.Add(block);
                }
            }

            if (upgradeableBlocks.Count == 0)
            {
                SendReply(player, config.NoBlocksMessage);
                return;
            }

            int upgradedCount = 0;
            foreach (var block in upgradeableBlocks)
            {
                if (UpgradeBlock(player, block, grade))
                {
                    upgradedCount++;
                    // Play upgrade sound if enabled
                    if (config.EnableUpgradeSounds)
                        PlayUpgradeSound(block, grade);
                }
            }

            if (upgradedCount > 0)
            {
                // Play a success sound for the player if enabled
                if (config.EnableUpgradeSounds)
                    PlayUpgradeSuccessSound(player);
                
                // Get grade name for message
                string gradeName = grade.ToString();
                SendReply(player, string.Format(config.UpgradeSuccessMessage, upgradedCount, gradeName));
            }
            else
                SendReply(player, config.InsufficientResourcesMessage);
        }

        private bool UpgradeBlock(BasePlayer player, BuildingBlock block, BuildingGrade.Enum grade)
        {
            // Check if downgrading is allowed
            if (!config.AllowDowngrading && (int)block.grade > (int)grade)
                return false;

            if (block.grade == grade)
                return false;

            // Skip resource check if not required
            if (!config.RequireResourcesForUpgrades)
            {
                block.SetGrade(grade);
                // Set health to maximum after upgrading
                block.health = block.MaxHealth();
                block.SendNetworkUpdate();
                return true;
            }

            // Get upgrade costs based on the block type and grade
            List<ItemAmount> cost = new List<ItemAmount>();
            
            // Add standard upgrade costs based on grade
            switch (grade)
            {
                case BuildingGrade.Enum.Wood:
                    cost.Add(new ItemAmount(ItemManager.FindItemDefinition("wood"), 200));
                    break;
                case BuildingGrade.Enum.Stone:
                    cost.Add(new ItemAmount(ItemManager.FindItemDefinition("stones"), 300));
                    break;
                case BuildingGrade.Enum.Metal:
                    cost.Add(new ItemAmount(ItemManager.FindItemDefinition("metal.fragments"), 200));
                    break;
                case BuildingGrade.Enum.TopTier:
                    cost.Add(new ItemAmount(ItemManager.FindItemDefinition("metal.refined"), 250));
                    break;
            }
            
            // Apply cost multiplier if enabled
            if (config.EnableCostMultiplier)
            {
                foreach (var item in cost)
                {
                    item.amount = Mathf.CeilToInt(item.amount * config.CostMultiplier);
                }
            }

            // Check if player has all required items
            bool hasAllItems = true;
            foreach (var item in cost)
            {
                int playerAmount = player.inventory.GetAmount(item.itemDef.itemid);
                if (playerAmount < item.amount)
                {
                    hasAllItems = false;
                    break;
                }
            }
            
            if (!hasAllItems)
                return false;

            // Take items and upgrade
            foreach (var item in cost)
            {
                player.inventory.Take(null, item.itemDef.itemid, (int)item.amount);
            }
            
            block.SetGrade(grade);
            // Set health to maximum after upgrading
            block.health = block.MaxHealth();
            block.SendNetworkUpdate();
            
            return true;
        }

        [ChatCommand("tcupgrader.position")]
        private void CommandPositionUI(BasePlayer player, string command, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, config.AdminPermission)) 
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }
            
            if (args.Length < 2)
            {
                SendReply(player, $"Usage: /{config.PositionCommand} <horizontal|vertical> <value>");
                return;
            }
            
            float value;
            if (!float.TryParse(args[1], out value))
            {
                SendReply(player, "Value must be a number");
                return;
            }
            
            switch (args[0].ToLower())
            {
                case "horizontal":
                case "h":
                    config.UIHorizontalOffset = value;
                    break;
                case "vertical":
                case "v":
                    config.UIVerticalOffset = value;
                    break;
                default:
                    SendReply(player, "First parameter must be 'horizontal' or 'vertical'");
                    return;
            }
            
            SaveConfig();
            SendReply(player, $"UI {args[0]} offset set to {value}");
            
            // Refresh UI for all players
            foreach (var p in BasePlayer.activePlayerList)
            {
                if (activeUIs.ContainsKey(p.userID))
                {
                    DestroyUI(p);
                    CreateUI(p);
                }
            }
        }

        [ChatCommand("tcupgrader.reload")]
        private void CommandReload(BasePlayer player, string command, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, config.AdminPermission))
            {
                SendReply(player, config.NoPermissionMessage);
                return;
            }
            
            LoadConfig();
            // Re-register images with potentially new URLs
            RegisterImages();
            
            SendReply(player, "TCUpgrader configuration reloaded!");
            
            // Refresh UI for all players
            foreach (var p in BasePlayer.activePlayerList)
            {
                if (activeUIs.ContainsKey(p.userID))
                {
                    DestroyUI(p);
                    CreateUI(p);
                }
            }
        }

        [ChatCommand("tcrepair")]
        private void CommandRepair(BasePlayer player, string command, string[] args)
        {
            if (!config.EnableRepair) return;
            
            if (config.EnablePermission && !permission.UserHasPermission(player.UserIDString, PERMISSION_USE))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }
            
            RepairBuilding(player);
        }

        [ConsoleCommand("tcrepair")]
        private void ConsoleRepair(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null) return;

            if (config.EnablePermission && !permission.UserHasPermission(player.UserIDString, PERMISSION_USE))
                return;

            RepairBuilding(player);
        }

        private void RepairBuilding(BasePlayer player)
        {
            // Remove debug message
            // PrintWarning($"RepairBuilding called for player {player.displayName}");
            
            BuildingPrivlidge tc = player.GetBuildingPrivilege();
            if (tc == null)
            {
                SendReply(player, config.NoTCMessage);
                return;
            }

            List<BuildingBlock> blocks = new List<BuildingBlock>();
            Vis.Entities(tc.transform.position, config.UpgradeRange, blocks, LayerMask.GetMask("Construction"));

            // Filter blocks that need repair
            List<BuildingBlock> repairableBlocks = new List<BuildingBlock>();
            foreach (var block in blocks)
            {
                if (block.buildingID == tc.buildingID && block.health < block.MaxHealth())
                {
                    repairableBlocks.Add(block);
                }
            }

            if (repairableBlocks.Count == 0)
            {
                SendReply(player, config.NoRepairBlocksMessage);
                return;
            }

            // Skip resource check if not required
            if (!config.RequireResourcesForRepairs)
            {
                // Repair all blocks without requiring resources
                foreach (var block in repairableBlocks)
                {
                    block.health = block.MaxHealth();
                    block.SendNetworkUpdate();
                    
                    // Play repair sound at block position if enabled
                    if (config.EnableRepairSounds)
                        PlayRepairSound(block);
                }
                
                // Play a repair sound for the player as well if enabled
                if (config.EnableRepairSounds)
                    PlayRepairSoundForPlayer(player);
                
                SendReply(player, string.Format(config.RepairSuccessMessage, repairableBlocks.Count));
                return;
            }

            // Calculate repair costs
            Dictionary<int, float> totalCosts = new Dictionary<int, float>();
            
            foreach (var block in repairableBlocks)
            {
                float healthFraction = 1f - (block.health / block.MaxHealth());
                
                // Get repair costs based on the block grade
                switch (block.grade)
                {
                    case BuildingGrade.Enum.Wood:
                        AddOrUpdateCost(totalCosts, ItemManager.FindItemDefinition("wood").itemid, 200 * healthFraction);
                        break;
                    case BuildingGrade.Enum.Stone:
                        AddOrUpdateCost(totalCosts, ItemManager.FindItemDefinition("stones").itemid, 300 * healthFraction);
                        break;
                    case BuildingGrade.Enum.Metal:
                        AddOrUpdateCost(totalCosts, ItemManager.FindItemDefinition("metal.fragments").itemid, 200 * healthFraction);
                        break;
                    case BuildingGrade.Enum.TopTier:
                        AddOrUpdateCost(totalCosts, ItemManager.FindItemDefinition("metal.refined").itemid, 100 * healthFraction);
                        break;
                }
            }
            
            // Apply repair cost multiplier
            foreach (var itemId in totalCosts.Keys.ToList())
            {
                totalCosts[itemId] = totalCosts[itemId] * config.RepairCostMultiplier;
            }
            
            // Check if player has all required items
            bool hasAllItems = true;
            string missingItems = "";
            
            foreach (var cost in totalCosts)
            {
                int requiredAmount = Mathf.CeilToInt(cost.Value);
                int playerAmount = player.inventory.GetAmount(cost.Key);
                
                if (playerAmount < requiredAmount)
                {
                    hasAllItems = false;
                    string itemName = ItemManager.FindItemDefinition(cost.Key).displayName.translated;
                    missingItems += $"\n{itemName}: {playerAmount}/{requiredAmount}";
                }
            }
            
            if (!hasAllItems)
            {
                SendReply(player, $"You don't have enough resources to repair all blocks. Missing: {missingItems}");
                return;
            }
            
            // Take items and repair
            foreach (var cost in totalCosts)
            {
                int amount = Mathf.CeilToInt(cost.Value);
                player.inventory.Take(null, cost.Key, amount);
            }
            
            // Repair all blocks and play repair sounds if enabled
            foreach (var block in repairableBlocks)
            {
                block.health = block.MaxHealth();
                block.SendNetworkUpdate();
                
                // Play repair sound at block position if enabled
                if (config.EnableRepairSounds)
                    PlayRepairSound(block);
            }
            
            // Play a repair sound for the player as well if enabled
            if (config.EnableRepairSounds)
                PlayRepairSoundForPlayer(player);
            
            SendReply(player, string.Format(config.RepairSuccessMessage, repairableBlocks.Count));
        }

        private void AddOrUpdateCost(Dictionary<int, float> costs, int itemId, float amount)
        {
            if (costs.ContainsKey(itemId))
                costs[itemId] += amount;
            else
                costs[itemId] = amount;
        }

        private void PlayRepairSound(BuildingBlock block)
        {
            // Choose appropriate repair sound based on block grade
            string soundEffect = "";
            
            switch (block.grade)
            {
                case BuildingGrade.Enum.Wood:
                    soundEffect = "assets/bundled/prefabs/fx/build/repair_wood.prefab";
                    break;
                case BuildingGrade.Enum.Stone:
                    soundEffect = "assets/bundled/prefabs/fx/build/repair_stone.prefab";
                    break;
                case BuildingGrade.Enum.Metal:
                    soundEffect = "assets/bundled/prefabs/fx/build/repair_metal.prefab";
                    break;
                case BuildingGrade.Enum.TopTier:
                    soundEffect = "assets/bundled/prefabs/fx/build/repair_metal.prefab";
                    break;
            }
            
            // Play the sound effect at the block's position
            if (!string.IsNullOrEmpty(soundEffect))
            {
                Effect.server.Run(soundEffect, block.transform.position);
            }
        }

        private void PlayRepairSoundForPlayer(BasePlayer player)
        {
            // Play a general repair complete sound for the player
            Effect.server.Run("assets/bundled/prefabs/fx/build/promote_metal.prefab", player.transform.position);
        }

        private void PlayUpgradeSound(BuildingBlock block, BuildingGrade.Enum grade)
        {
            // Choose appropriate upgrade sound based on the target grade
            string soundEffect = "";
            
            switch (grade)
            {
                case BuildingGrade.Enum.Wood:
                    soundEffect = "assets/bundled/prefabs/fx/build/promote_wood.prefab";
                    break;
                case BuildingGrade.Enum.Stone:
                    soundEffect = "assets/bundled/prefabs/fx/build/promote_stone.prefab";
                    break;
                case BuildingGrade.Enum.Metal:
                    soundEffect = "assets/bundled/prefabs/fx/build/promote_metal.prefab";
                    break;
                case BuildingGrade.Enum.TopTier:
                    soundEffect = "assets/bundled/prefabs/fx/build/promote_toptier.prefab";
                    break;
            }
            
            // Play the sound effect at the block's position
            if (!string.IsNullOrEmpty(soundEffect))
            {
                Effect.server.Run(soundEffect, block.transform.position);
            }
        }

        private void PlayUpgradeSuccessSound(BasePlayer player)
        {
            // Use a different sound effect that's guaranteed to exist
            Effect.server.Run("assets/bundled/prefabs/fx/build/promote_metal.prefab", player.transform.position);
        }

        private void SendReply(BasePlayer player, string message)
        {
            if (config.EnableCustomMessages)
            {
                // Determine if this is an error message
                bool isError = message.Contains("Failed") || 
                               message.Contains("don't have") || 
                               message.Contains("need to be") || 
                               message.Contains("No blocks") ||
                               message.Contains("not enough");
                
                // Add appropriate prefix
                string prefix = isError ? config.ErrorMessagePrefix : config.SuccessMessagePrefix;
                
                // Add image if enabled
                if (config.UseMessageImages)
                {
                    string imageUrl = isError ? config.ErrorMessageImageUrl : config.SuccessMessageImageUrl;
                    string imageTag = GetMessageImageTag(imageUrl, config.MessageImageSize);
                    message = imageTag + " " + message;
                }
                else
                {
                    message = prefix + message;
                }
            }
            
            // Send the message to the player
            player.ChatMessage(message);
        }

        private string GetMessageImageTag(string imageUrl, int size)
        {
            // Generate an img tag for the chat message
            return $"<img src=\"{imageUrl}\" width=\"{size}\" height=\"{size}\">";
        }

        #endregion
    }
}

























































