using System;
using System.Collections.Generic;
using Oxide.Core;
using Oxide.Core.Plugins;
using UnityEngine;
using Newtonsoft.Json;

namespace Oxide.Plugins
{
    [Info("Drone Storage", "Frizzo420", "1.0.3")]
    [Description("Adds small storage capability to drones")]
    class DroneStorage : RustPlugin
    {
        #region Configuration
        
        private Configuration config;
        
        class Configuration
        {
            [JsonProperty("Storage Prefab")]
            public string StoragePrefab = "assets/prefabs/deployable/woodenbox/woodbox_deployed.prefab";
            
            [JsonProperty("Light Prefab")]
            public string LightPrefab = "assets/prefabs/deployable/lantern/lantern.deployed.prefab";
            
            [JsonProperty("Storage Capacity")]
            public int StorageCapacity = 6;
            
            [JsonProperty("Default Storage Capacity")]
            public int DefaultStorageCapacity = 6;
            
            [JsonProperty("Vip Storage Capacity")]
            public int VipStorageCapacity = 12;
            
            [JsonProperty("Admin Storage Capacity")]
            public int AdminStorageCapacity = 24;
            
            [JsonProperty("Storage Position (Y offset)")]
            public float StoragePositionY = 0.3f;
            
            [JsonProperty("Storage Scale")]
            public float StorageScale = 0.6f;
            
            [JsonProperty("Light Position (Y offset)")]
            public float LightPositionY = 0.5f;
            
            [JsonProperty("Light Scale")]
            public float LightScale = 0.5f;
            
            [JsonProperty("Notify Owner On Drone Spawn")]
            public bool BroadcastOnSpawn = true;
            
            [JsonProperty("Owner Notification Message")]
            public string BroadcastMessage = "Your drone with storage has been deployed! Use /openbox when near it.";
            
            [JsonProperty("Interaction Distance")]
            public float InteractionDistance = 5f;
            
            [JsonProperty("Enable Storage For All Drones")]
            public bool EnableForAllDrones = true;
            
            [JsonProperty("Enable Custom Drone Skins")]
            public bool EnableCustomSkins = false;
            
            [JsonProperty("Custom Drone Skin IDs")]
            public Dictionary<string, ulong> CustomSkins = new Dictionary<string, ulong>
            {
                ["default"] = 0,
                ["vip"] = 0,
                ["admin"] = 0
            };
            
            [JsonProperty("Storage Types")]
            public Dictionary<string, string> StorageTypes = new Dictionary<string, string>
            {
                ["default"] = "assets/prefabs/deployable/woodenbox/woodbox_deployed.prefab",
                ["vip"] = "assets/prefabs/deployable/woodenbox/woodbox_deployed.prefab",
                ["admin"] = "assets/prefabs/deployable/woodenbox/woodbox_deployed.prefab"
            };
            
            [JsonProperty("Enable Drone Fuel Consumption Modifier")]
            public bool EnableFuelModifier = false;
            
            [JsonProperty("Drone Fuel Consumption Multiplier")]
            public float FuelConsumptionMultiplier = 1.0f;
            
            [JsonProperty("Enable Drone Speed Modifier")]
            public bool EnableSpeedModifier = false;
            
            [JsonProperty("Drone Speed Multiplier")]
            public float SpeedMultiplier = 1.0f;
            
            [JsonProperty("Enable Cooldown Between Storage Access")]
            public bool EnableCooldown = false;
            
            [JsonProperty("Cooldown Time (seconds)")]
            public float CooldownTime = 30f;
            
            [JsonProperty("Enable Storage Access Logs")]
            public bool EnableAccessLogs = false;
        }
        
        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null) LoadDefaultConfig();
            }
            catch
            {
                PrintWarning("Configuration file is corrupt, creating new configuration file!");
                LoadDefaultConfig();
            }
            SaveConfig();
        }
        
        protected override void LoadDefaultConfig()
        {
            config = new Configuration();
            PrintWarning("Default configuration file created!");
        }
        
        protected override void SaveConfig() => Config.WriteObject(config);
        
        #endregion
        
        private Dictionary<NetworkableId, StorageContainer> droneStorage = new Dictionary<NetworkableId, StorageContainer>();
        private Dictionary<NetworkableId, BaseEntity> droneLights = new Dictionary<NetworkableId, BaseEntity>();
        private Dictionary<ulong, float> playerCooldowns = new Dictionary<ulong, float>();
        private Dictionary<NetworkableId, string> droneTypes = new Dictionary<NetworkableId, string>();

        void OnServerInitialized()
        {
            // Register permissions
            permission.RegisterPermission("dronestorage.use", this);
            permission.RegisterPermission("dronestorage.admin", this);
            permission.RegisterPermission("dronestorage.vip", this);
            
            Puts("DroneStorage plugin initialized!");
            
            // Load config if not already loaded
            if (config == null) LoadConfig();
        }

        void Unload()
        {
            foreach (var storage in droneStorage.Values)
            {
                if (storage != null && !storage.IsDestroyed)
                    storage.Kill();
            }
            
            foreach (var light in droneLights.Values)
            {
                if (light != null && !light.IsDestroyed)
                    light.Kill();
            }
        }

        void OnEntitySpawned(BaseNetworkable entity)
        {
            try
            {
                var drone = entity as Drone;
                if (drone == null) return;
                
                Puts($"Drone spawned: {drone.net.ID}");
                
                // Add a small delay to ensure the drone is fully initialized
                timer.Once(2f, () => {
                    if (drone != null && !drone.IsDestroyed)
                        CreateStorageForDrone(drone);
                });
            }
            catch (Exception ex)
            {
                Puts($"Error in OnEntitySpawned: {ex.Message}");
            }
        }

        private void CreateStorageForDrone(Drone drone)
        {
            try
            {
                if (drone == null || drone.IsDestroyed) return;
                
                if (droneStorage.ContainsKey(drone.net.ID)) 
                {
                    Puts($"Storage already exists for drone {drone.net.ID}");
                    return;
                }
                
                // Skip if not enabled for all drones and no owner permissions
                if (!config.EnableForAllDrones && 
                    !permission.UserHasPermission(drone.OwnerID.ToString(), "dronestorage.use") &&
                    !permission.UserHasPermission(drone.OwnerID.ToString(), "dronestorage.vip") &&
                    !permission.UserHasPermission(drone.OwnerID.ToString(), "dronestorage.admin"))
                {
                    return;
                }
                
                Puts($"Creating storage for drone {drone.net.ID}");
                
                // Determine storage type based on permissions
                string storageType = "default";
                if (permission.UserHasPermission(drone.OwnerID.ToString(), "dronestorage.admin"))
                    storageType = "admin";
                else if (permission.UserHasPermission(drone.OwnerID.ToString(), "dronestorage.vip"))
                    storageType = "vip";
                
                droneTypes[drone.net.ID] = storageType;
                
                // Get the appropriate storage prefab
                string storagePrefab = config.StorageTypes.ContainsKey(storageType) 
                    ? config.StorageTypes[storageType] 
                    : config.StoragePrefab;
                
                // Create the storage entity
                var storage = GameManager.server.CreateEntity(storagePrefab) as StorageContainer;
                if (storage == null)
                {
                    Puts($"Failed to create storage entity from prefab: {config.StoragePrefab}");
                    return;
                }
                
                // Set position and parent before spawning
                storage.transform.position = drone.transform.position;
                storage.transform.SetParent(drone.transform, true);
                
                // Position the box on top of the drone
                storage.transform.localPosition = new Vector3(0, config.StoragePositionY, 0);
                storage.transform.localScale = new Vector3(config.StorageScale, config.StorageScale, config.StorageScale);
                
                // Set owner ID
                storage.OwnerID = drone.OwnerID;
                
                // Important: Disable collision to prevent physics issues
                var colliders = storage.GetComponentsInChildren<Collider>();
                foreach (var collider in colliders)
                {
                    collider.enabled = false;
                }
                
                // Spawn the entity
                storage.Spawn();
                
                // Add a light to make it more visible
                var light = GameManager.server.CreateEntity(config.LightPrefab) as BaseEntity;
                if (light != null)
                {
                    light.transform.position = drone.transform.position;
                    light.transform.SetParent(drone.transform, true);
                    light.transform.localPosition = new Vector3(0, config.LightPositionY, 0);
                    light.transform.localScale = new Vector3(config.LightScale, config.LightScale, config.LightScale);
                    light.Spawn();
                    droneLights[drone.net.ID] = light;
                }
                
                // Configure after spawning
                timer.Once(0.1f, () => {
                    if (storage == null || storage.IsDestroyed) return;
                    
                    if (storage.inventory == null)
                    {
                        Puts("Error: Storage inventory is null after delay");
                        storage.Kill();
                        return;
                    }
                    
                    // Set storage capacity based on owner's permissions
                    int capacity = config.DefaultStorageCapacity;
                    ulong ownerId = drone.OwnerID;
                    string ownerIdString = ownerId.ToString();
                    
                    if (permission.UserHasPermission(ownerIdString, "dronestorage.admin"))
                    {
                        capacity = config.AdminStorageCapacity;
                    }
                    else if (permission.UserHasPermission(ownerIdString, "dronestorage.vip"))
                    {
                        capacity = config.VipStorageCapacity;
                    }
                    
                    storage.inventory.capacity = capacity;
                    Puts($"Created storage for drone {drone.net.ID} with capacity: {capacity}");
                    
                    // Configure storage properties
                    storage.SetFlag(BaseEntity.Flags.Open, true); // Try to make it visually open
                    
                    // Add to dictionary
                    droneStorage[drone.net.ID] = storage;
                    
                    Puts($"Storage successfully created for drone {drone.net.ID}");
                    
                    // Broadcast to the owner player if enabled
                    if (config.BroadcastOnSpawn)
                    {
                        // Find the owner player if they're online
                        BasePlayer ownerPlayer = BasePlayer.FindByID(drone.OwnerID);
                        if (ownerPlayer != null)
                        {
                            SendReply(ownerPlayer, config.BroadcastMessage);
                        }
                    }
                });
                
                // Apply custom skin if enabled
                if (config.EnableCustomSkins && config.CustomSkins.ContainsKey(storageType))
                {
                    ulong skinId = config.CustomSkins[storageType];
                    if (skinId > 0)
                        drone.skinID = skinId;
                }
                
                // Apply speed modifier if enabled
                if (config.EnableSpeedModifier)
                {
                    // This would need to hook into the drone's movement system
                    // Implementation depends on how drone speed is controlled in Rust
                }
            }
            catch (Exception ex)
            {
                Puts($"Error in CreateStorageForDrone: {ex.Message}");
            }
        }

        void OnEntityKill(BaseNetworkable entity)
        {
            var drone = entity as Drone;
            if (drone == null) return;
            
            if (droneStorage.TryGetValue(drone.net.ID, out var storage))
            {
                if (storage != null && !storage.IsDestroyed)
                    storage.Kill();
                    
                droneStorage.Remove(drone.net.ID);
                Puts($"Storage removed for drone {drone.net.ID}");
            }
            
            if (droneLights.TryGetValue(drone.net.ID, out var light))
            {
                if (light != null && !light.IsDestroyed)
                    light.Kill();
                    
                droneLights.Remove(drone.net.ID);
            }
        }
        
        // Add a command to help players find the storage
        [ChatCommand("dronestorage")]
        void DroneStorageCommand(BasePlayer player, string command, string[] args)
        {
            // Check if player has admin permission
            if (!permission.UserHasPermission(player.UserIDString, "dronestorage.admin"))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }
            
            // Find nearby drones
            var nearbyDrones = new List<Drone>();
            Vis.Entities(player.transform.position, 10f, nearbyDrones);
            
            if (nearbyDrones.Count == 0)
            {
                SendReply(player, "No drones found nearby.");
                return;
            }
            
            int count = 0;
            foreach (var drone in nearbyDrones)
            {
                if (droneStorage.ContainsKey(drone.net.ID))
                {
                    count++;
                    // Send a message with the distance
                    float distance = Vector3.Distance(player.transform.position, drone.transform.position);
                    SendReply(player, $"Drone with storage found {distance:F1}m away.");
                }
            }
            
            if (count == 0)
                SendReply(player, "No drones with storage found nearby.");
        }

        // Add a chat command to open the storage
        [ChatCommand("openbox")]
        void OpenBoxCommand(BasePlayer player, string command, string[] args)
        {
            // Find nearby drones
            var nearbyDrones = new List<Drone>();
            Vis.Entities(player.transform.position, config.InteractionDistance, nearbyDrones);
            
            if (nearbyDrones.Count == 0)
            {
                SendReply(player, "No drones found nearby.");
                return;
            }
            
            // Find the closest drone with storage
            Drone closestDrone = null;
            float closestDistance = float.MaxValue;
            
            foreach (var drone in nearbyDrones)
            {
                if (droneStorage.ContainsKey(drone.net.ID))
                {
                    float distance = Vector3.Distance(player.transform.position, drone.transform.position);
                    if (distance < closestDistance)
                    {
                        closestDistance = distance;
                        closestDrone = drone;
                    }
                }
            }
            
            if (closestDrone != null && droneStorage.TryGetValue(closestDrone.net.ID, out var storage))
            {
                if (storage != null && !storage.IsDestroyed)
                {
                    // Debug info
                    Puts($"Opening storage for player {player.displayName}, drone ID: {closestDrone.net.ID}");
                    
                    // Try multiple approaches to open the storage
                    try {
                        // Approach 1: Direct method call
                        storage.PlayerOpenLoot(player);
                        
                        // Approach 2: Use timer with short delay
                        timer.Once(0.1f, () => {
                            if (player != null && !player.IsDestroyed && storage != null && !storage.IsDestroyed)
                            {
                                storage.PlayerOpenLoot(player);
                            }
                        });
                        
                        // Approach 3: Force player to look at storage first
                        player.SendConsoleCommand("look", storage.transform.position - player.eyes.position);
                        
                        // Approach 4: Use AddContainer method if available
                        if (player.inventory != null && player.inventory.loot != null)
                        {
                            player.inventory.loot.AddContainer(storage.inventory);
                            player.inventory.loot.SendImmediate();
                        }
                        
                        SendReply(player, "Opening drone storage box. If it doesn't open, try looking directly at the box.");
                    }
                    catch (Exception ex) {
                        Puts($"Error opening storage: {ex.Message}");
                        SendReply(player, "Error opening storage. Try again or report to admin.");
                    }
                }
                else
                {
                    SendReply(player, "Storage exists but is invalid. Please report this to an admin.");
                    Puts($"Invalid storage for drone {closestDrone.net.ID}. Storage is null or destroyed.");
                }
            }
            else
            {
                SendReply(player, "No drones with storage found nearby.");
            }
        }
        
        // Add this hook to handle player interactions with the storage directly
        object OnEntityUse(BasePlayer player, BaseEntity entity)
        {
            // Check if the entity is a storage container from our plugin
            foreach (var pair in droneStorage)
            {
                if (pair.Value == entity)
                {
                    Puts($"Player {player.displayName} directly interacted with drone storage {pair.Key}");
                    return null; // Allow the default interaction
                }
            }
            
            // Check if the entity is a drone
            var drone = entity as Drone;
            if (drone == null) return null;
            
            // Check if this drone has storage
            if (droneStorage.TryGetValue(drone.net.ID, out var storage))
            {
                if (storage != null && !storage.IsDestroyed)
                {
                    Puts($"Player {player.displayName} interacted with drone {drone.net.ID}, opening storage");
                    
                    // Try multiple approaches to open the storage
                    try {
                        // Approach 1: Direct method call
                        storage.PlayerOpenLoot(player);
                        
                        // Approach 2: Use timer with short delay
                        timer.Once(0.1f, () => {
                            if (player != null && !player.IsDestroyed && storage != null && !storage.IsDestroyed)
                            {
                                storage.PlayerOpenLoot(player);
                                
                                // Approach 3: Use AddContainer method if available
                                if (player.inventory != null && player.inventory.loot != null)
                                {
                                    player.inventory.loot.AddContainer(storage.inventory);
                                    player.inventory.loot.SendImmediate();
                                }
                            }
                        });
                        
                        SendReply(player, "Opening drone storage box.");
                    }
                    catch (Exception ex) {
                        Puts($"Error opening storage: {ex.Message}");
                    }
                    
                    return true; // Prevent default drone interaction
                }
            }
            
            return null; // Allow default interaction
        }
        
        // Add a command to reload the configuration
        [ChatCommand("dronereload")]
        void ReloadConfigCommand(BasePlayer player, string command, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, "dronestorage.admin"))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }
            
            LoadConfig();
            SendReply(player, "DroneStorage configuration reloaded!");
        }
        
        // Add a method to handle storage access with cooldown
        private bool CanAccessStorage(BasePlayer player)
        {
            if (!config.EnableCooldown)
                return true;
                
            ulong playerId = player.userID;
            float currentTime = Time.realtimeSinceStartup;
            
            if (playerCooldowns.TryGetValue(playerId, out float lastAccessTime))
            {
                float timeRemaining = lastAccessTime + config.CooldownTime - currentTime;
                if (timeRemaining > 0)
                {
                    SendReply(player, $"You must wait {timeRemaining:F1} seconds before accessing drone storage again.");
                    return false;
                }
            }
            
            playerCooldowns[playerId] = currentTime;
            return true;
        }
        
        // Add a method to log storage access
        private void LogStorageAccess(BasePlayer player, StorageContainer storage)
        {
            if (!config.EnableAccessLogs)
                return;
                
            string logEntry = $"{DateTime.Now} - Player {player.displayName} ({player.userID}) accessed storage on drone";
            LogToFile("storage_access", logEntry, this);
        }
    }
}





